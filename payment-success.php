<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/EmailService.php';

// Initialize models
$paymentModel = new Payment();
$bookingModel = new Booking();
$quoteModel = new Quote();
$emailService = new EmailService();

// Get payment ID from URL or quote reference
$paymentId = $_GET['payment_id'] ?? null;
$quoteRef = $_GET['quote_ref'] ?? null;
$payment = null;
$booking = null;
$quote = null;
$message = '';

$successMessage = '';
$isPartialPayment = false;
$paymentAmount = 0;
$balanceRemaining = 0;

if ($paymentId) {
    // Handle traditional payment ID approach
    $payment = $paymentModel->findWithBookingDetails($paymentId);

    if (!$payment) {
        $message = 'Payment not found.';
    } elseif ($payment['payment_status'] === 'failed') {
        $message = 'Payment failed. Please try again.';
    } elseif ($payment['payment_status'] === 'pending') {
        $successMessage = 'Payment is being processed. You will receive confirmation once completed.';
    } elseif ($payment['payment_status'] === 'completed') {
        $paymentAmount = $payment['amount'];

        if ($payment['booking_id']) {
            // Get full booking details for booking-based payment
            $booking = $bookingModel->findWithDetails($payment['booking_id']);
            $successMessage = 'Payment successful! Your booking has been confirmed.';
        } elseif ($payment['quote_id']) {
            // Get quote details for quote-based payment with payment summary
            $quote = $quoteModel->getQuoteWithPaymentSummary($payment['quote_id']);

            if ($quote) {
                $balanceRemaining = $quote['balance_remaining'];
                $isPartialPayment = $balanceRemaining > 0;

                if ($isPartialPayment) {
                    $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Balance remaining: $" . number_format($balanceRemaining, 2);
                } else {
                    $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Your quote is now fully paid.";
                }

                // Send payment confirmation email
                try {
                    $emailData = [
                        'customer_name' => $quote['customer_name'],
                        'customer_email' => $quote['customer_email'],
                        'quote_reference' => $quote['quote_reference'],
                        'payment_reference' => $payment['payment_reference'],
                        'amount_paid' => $paymentAmount,
                        'quoted_amount' => $quote['quoted_amount'],
                        'total_paid' => $quote['total_paid'],
                        'balance_remaining' => $balanceRemaining,
                        'travel_date' => $quote['travel_date'],
                        'number_of_adults' => $quote['number_of_adults'],
                        'number_of_children' => $quote['number_of_children'],
                        'special_requirements' => $quote['special_requirements']
                    ];

                    // Always send payment receipt (it handles both partial and full payments)
                    if ($isPartialPayment) {
                        // Generate payment link for remaining balance
                        $paymentLink = $paymentModel->generatePaymentLink($quote['quote_id'], $balanceRemaining, 'balance');
                        $emailData['payment_link'] = $paymentLink;
                    }
                    $emailService->sendPaymentReceipt($emailData);
                } catch (Exception $e) {
                    error_log("Failed to send payment confirmation email: " . $e->getMessage());
                }
            }
        }
    } else {
        $message = 'Payment status unknown. Please contact support.';
    }
} elseif ($quoteRef) {
    // Handle simplified quote reference approach
    $quote = $quoteModel->getQuoteWithPaymentSummary($quoteModel->findByReference($quoteRef)['quote_id'] ?? null);

    if (!$quote) {
        $message = 'Quote not found.';
    } else {
        // Check if there are any completed payments for this quote
        $recentPayments = $paymentModel->getByQuoteId($quote['quote_id']);
        $hasCompletedPayments = false;
        $latestPayment = null;

        foreach ($recentPayments as $recentPayment) {
            if ($recentPayment['payment_status'] === 'completed') {
                $hasCompletedPayments = true;
                if (!$latestPayment || $recentPayment['payment_date'] > $latestPayment['payment_date']) {
                    $latestPayment = $recentPayment;
                }
            }
        }

        if ($hasCompletedPayments && $latestPayment) {
            $paymentAmount = $latestPayment['amount'];
            $balanceRemaining = $quote['balance_remaining'];
            $isPartialPayment = $balanceRemaining > 0;

            if ($isPartialPayment) {
                $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Balance remaining: $" . number_format($balanceRemaining, 2);
            } else {
                $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Your quote is now fully paid.";
            }

            // Send payment confirmation email
            try {
                $emailData = [
                    'customer_name' => $quote['customer_name'],
                    'customer_email' => $quote['customer_email'],
                    'quote_reference' => $quote['quote_reference'],
                    'payment_reference' => $latestPayment['payment_reference'],
                    'amount_paid' => $paymentAmount,
                    'quoted_amount' => $quote['quoted_amount'],
                    'total_paid' => $quote['total_paid'],
                    'balance_remaining' => $balanceRemaining,
                    'travel_date' => $quote['travel_date'],
                    'number_of_adults' => $quote['number_of_adults'],
                    'number_of_children' => $quote['number_of_children'],
                    'special_requirements' => $quote['special_requirements']
                ];

                // Always send payment receipt (it handles both partial and full payments)
                if ($isPartialPayment) {
                    // Generate payment link for remaining balance
                    $paymentLink = $paymentModel->generatePaymentLink($quote['quote_id'], $balanceRemaining, 'balance');
                    $emailData['payment_link'] = $paymentLink;
                }
                $emailService->sendPaymentReceipt($emailData);
            } catch (Exception $e) {
                error_log("Failed to send payment confirmation email: " . $e->getMessage());
            }
        } else {
            $message = 'Payment has not been completed yet. Please try again or contact support.';
        }
    }
} else {
    $message = 'Payment ID or quote reference is required.';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-50">
    <?php include 'header.php'; ?>

    <!-- Success Section -->
    <section class="py-16">
        <div class="max-w-4xl mx-auto px-6">
            
            <?php if ($message): ?>
                <div class="text-center">
                    <div class="bg-red-100 text-red-800 border border-red-200 p-6 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <h2 class="text-2xl font-bold mb-2">Error</h2>
                        <p><?php echo htmlspecialchars($message); ?></p>
                        <div class="mt-6">
                            <a href="index.php" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                                <i class="fas fa-home mr-2"></i>Return Home
                            </a>
                        </div>
                    </div>
                </div>
            <?php elseif ($successMessage): ?>
                <!-- Success Message with Layout from Photo -->
                <div class="text-center mb-8">
                    <div class="bg-green-100 border border-green-200 p-8 rounded-lg">
                        <div class="bg-white rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-check text-2xl text-green-600"></i>
                        </div>
                        <h1 class="text-3xl font-bold text-green-800 mb-2">Payment Successful!</h1>
                        <p class="text-green-700 mb-6">Thank you for your payment. Your booking has been confirmed.</p>

                        <!-- Payment Details Box -->
                        <div class="bg-white rounded-lg p-4 mb-4 max-w-md mx-auto">
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Payment Details</h3>
                            <div class="grid grid-cols-2 gap-3 text-sm">
                                <div class="text-left">
                                    <p class="text-gray-600">Quote Reference:</p>
                                    <p class="font-semibold"><?php echo htmlspecialchars($payment['booking_reference'] ?? $quote['quote_reference']); ?></p>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-600">Transaction:</p>
                                    <p class="font-semibold text-green-600">Successful</p>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-600">Amount Paid:</p>
                                    <p class="font-semibold">$<?php echo number_format($payment['amount'] ?? $paymentAmount, 2); ?></p>
                                </div>
                                <div class="text-left">
                                    <!-- Empty for spacing -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Travel Quote Summary Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Travel Quote Summary</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Customer Information -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">Customer Information</h3>
                        <div class="space-y-2">
                            <?php if ($payment && isset($payment['customer_name'])): ?>
                                <div>
                                    <span class="text-gray-600">Name:</span>
                                    <span class="font-medium ml-2"><?php echo htmlspecialchars($payment['customer_name']); ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Email:</span>
                                    <span class="font-medium ml-2"><?php echo htmlspecialchars($payment['customer_email']); ?></span>
                                </div>
                            <?php elseif ($quote): ?>
                                <div>
                                    <span class="text-gray-600">Name:</span>
                                    <span class="font-medium ml-2"><?php echo htmlspecialchars($quote['customer_name']); ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Email:</span>
                                    <span class="font-medium ml-2"><?php echo htmlspecialchars($quote['customer_email']); ?></span>
                                </div>
                            <?php endif; ?>
                            <div>
                                <span class="text-gray-600">Quote Status:</span>
                                <?php
                                $statusColor = 'text-green-600';
                                $statusText = 'Paid';
                                if ($quote && isset($quote['payment_status'])) {
                                    if ($quote['payment_status'] === 'partial') {
                                        $statusColor = 'text-orange-600';
                                        $statusText = 'Partially Paid';
                                    } elseif ($quote['payment_status'] === 'paid') {
                                        $statusColor = 'text-green-600';
                                        $statusText = 'Paid';
                                    } else {
                                        $statusColor = 'text-gray-600';
                                        $statusText = ucfirst($quote['payment_status']);
                                    }
                                }
                                ?>
                                <span class="font-medium ml-2 <?php echo $statusColor; ?>"><?php echo $statusText; ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Travel Details -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">Travel Details</h3>
                        <div class="space-y-2">
                            <?php if ($quote): ?>
                                <div>
                                    <span class="text-gray-600">Travel Date:</span>
                                    <span class="font-medium ml-2"><?php echo $quote['travel_date'] ? date('F j, Y', strtotime($quote['travel_date'])) : 'To be determined'; ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Travelers:</span>
                                    <span class="font-medium ml-2"><?php echo $quote['number_of_adults']; ?> Adult(s)<?php echo $quote['number_of_children'] > 0 ? ', ' . $quote['number_of_children'] . ' Child(ren)' : ''; ?></span>
                                </div>
                            <?php elseif ($booking): ?>
                                <div>
                                    <span class="text-gray-600">Travel Date:</span>
                                    <span class="font-medium ml-2"><?php echo date('F j, Y', strtotime($booking['travel_date'])); ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Travelers:</span>
                                    <span class="font-medium ml-2"><?php echo $booking['number_of_adults']; ?> Adult(s)<?php echo $booking['number_of_children'] > 0 ? ', ' . $booking['number_of_children'] . ' Child(ren)' : ''; ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>







                <!-- Additional Payment Option for Partial Payments -->
                <?php if ($isPartialPayment && $balanceRemaining > 0): ?>
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                        <h3 class="text-xl font-bold text-orange-800 mb-4">
                            <i class="fas fa-credit-card mr-2"></i>Complete Your Payment
                        </h3>
                        <p class="text-orange-700 mb-4">
                            You have a remaining balance of <strong>$<?php echo number_format($balanceRemaining, 2); ?></strong>.
                            You can make an additional payment anytime to complete your booking.
                        </p>
                        <div class="text-center">
                            <a href="payment.php?quote_ref=<?php echo urlencode($quote['quote_reference'] ?? ''); ?>"
                               class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                                <i class="fas fa-plus mr-2"></i>Make Additional Payment
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- What's Next Section (like in photo) -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">
                        <i class="fas fa-info-circle mr-2"></i>What's Next?
                    </h3>
                    <div class="space-y-1 text-blue-700">
                        <p><i class="fas fa-envelope mr-2"></i>You will receive a confirmation email with your booking details and receipt.</p>
                        <p><i class="fas fa-phone mr-2"></i>Our team will contact you to confirm your travel arrangements.</p>
                        <p><i class="fas fa-bookmark mr-2"></i>Please keep your reference <strong><?php echo htmlspecialchars($payment['booking_reference'] ?? $quote['quote_reference']); ?></strong> for future correspondence.</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center space-y-3">
                    <div class="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                        <button onclick="window.print()"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                            <i class="fas fa-print mr-2"></i>Print Receipt
                        </button>

                        <a href="contact.php"
                           class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-headset mr-2"></i>Contact Support
                        </a>

                        <a href="index.php"
                           class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-home mr-2"></i>Return Home
                        </a>
                    </div>

                    <p class="text-sm text-gray-600 mt-4">
                        Need help? Contact us at
                        <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium"><EMAIL></a>
                        or call us at
                        <a href="tel:+254123456789" class="text-orange-600 hover:text-orange-700 font-medium">+254 123 456 789</a>
                    </p>
                </div>
        </div>
    </section>

    <?php include 'footer.php'; ?>

    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .bg-green-100, .bg-blue-50 {
                background: white !important;
                border: 1px solid #ccc !important;
            }
        }
    </style>
</body>
</html>
