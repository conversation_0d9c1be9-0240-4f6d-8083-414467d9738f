<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';

// Require authentication
Auth::requireLogin();

$contactModel = new ContactInfo();
$currentUser = Auth::getCurrentUser();

// Function to safely sanitize map embed code
function sanitizeMapEmbedCode($input) {
    if (empty($input)) {
        return '';
    }

    // Only allow iframe tags from Google Maps
    $input = trim($input);

    // Check if it's a valid Google Maps iframe
    if (preg_match('/^<iframe[^>]*src=["\']https:\/\/www\.google\.com\/maps\/embed[^"\']*["\'][^>]*><\/iframe>$/i', $input)) {
        // Additional security: ensure no javascript or other dangerous attributes
        $input = preg_replace('/on\w+=["\'][^"\']*["\']/', '', $input); // Remove event handlers
        $input = preg_replace('/javascript:/i', '', $input); // Remove javascript: URLs
        return $input;
    }

    return ''; // Return empty if not a valid Google Maps iframe
}

// Get dashboard statistics for sidebar
try {
    $reportModel = new Report();
    $stats = $reportModel->generateDashboardStats();
} catch (Exception $e) {
    // Fallback stats if dashboard stats fail
    $stats = [
        'destinations' => 0,
        'tour_packages' => 0,
        'messages' => 0,
        'unread_messages' => 0,
        'total_quotes' => 0,
        'pending_quotes' => 0,
        'quoted_quotes' => 0,
        'accepted_quotes' => 0,
        'total_bookings' => 0,
        'active_bookings' => 0,
        'total_booking_value' => 0,
        'total_payments' => 0,
        'pending_payments' => 0,
        'total_revenue' => 0,
        'recent_activity' => []
    ];
    error_log("Dashboard stats error in contact-info.php: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update') {
        try {
            $id = intval($_POST['id']);
            $data = [
                'phone_number' => Utils::sanitizeInput($_POST['phone_number']),
                'email' => Utils::sanitizeInput($_POST['email']),
                'booking_email' => Utils::sanitizeInput($_POST['booking_email']),
                'address' => Utils::sanitizeInput($_POST['address']),
                'working_hours' => Utils::sanitizeInput($_POST['working_hours']),
                'map_embed_code' => sanitizeMapEmbedCode($_POST['map_embed_code']),
                'facebook_url' => Utils::sanitizeInput($_POST['facebook_url']),
                'twitter_url' => Utils::sanitizeInput($_POST['twitter_url']),
                'instagram_url' => Utils::sanitizeInput($_POST['instagram_url']),
                'tiktok_url' => Utils::sanitizeInput($_POST['tiktok_url']),
                'youtube_url' => Utils::sanitizeInput($_POST['youtube_url']),
                'last_edited_by_user_id' => $currentUser['user_id']
            ];

            if ($contactModel->update($id, $data)) {
                $success = 'Contact information updated successfully!';
            } else {
                $error = 'Failed to update contact information.';
            }
        } catch (Exception $e) {
            $error = 'Error updating contact information: ' . $e->getMessage();
            error_log("Contact info update error: " . $e->getMessage());
        }
    }
}

// Get current contact info with user information
try {
    $contactInfo = $contactModel->getContactWithUserInfo();
} catch (Exception $e) {
    error_log("Error getting contact info: " . $e->getMessage());
    // Fallback to basic method
    try {
        $contactInfo = $contactModel->getCurrent();
    } catch (Exception $e2) {
        error_log("Error getting basic contact info: " . $e2->getMessage());
        $contactInfo = [
            'contact_id' => 1,
            'phone_number' => '',
            'email' => '',
            'address' => '',
            'working_hours' => '',
            'map_embed_code' => '',
            'facebook_url' => '',
            'twitter_url' => '',
            'instagram_url' => '',
            'tiktok_url' => '',
            'youtube_url' => '',
            'last_edited_by_user_id' => null,
            'updated_at' => null
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Info - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        /* Sidebar Styles */
        #sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            width: 16rem;
            z-index: 50;
            transition: transform 0.3s ease;
        }
        
        .main-content {
            margin-left: 16rem;
            transition: margin-left 0.3s ease;
        }
        
        /* Mobile Responsive */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }

        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Map iframe styling for responsive display */
        .map-container iframe {
            width: 100% !important;
            height: 100% !important;
            border: none;
            border-radius: 1rem;
        }

        /* Ensure map preview container matches frontend styling */
        .h-96 {
            height: 24rem;
        }

        .rounded-2xl {
            border-radius: 1rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Contact Information</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6"><main class="p-6">
            <!-- Page Description -->
            <div class="mb-6">
                <p class="text-gray-600">Manage business contact details and location</p>
            </div>

            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Contact Information Form -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Business Contact Information</h3>
                    <p class="text-sm text-gray-600 mt-1">Update your business contact details that appear on the website</p>
                </div>

                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $contactInfo['contact_id'] ?? 1; ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Phone Number -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-phone mr-2 text-orange-500"></i>Phone Number
                            </label>
                            <input type="tel"
                                   id="phone_number"
                                   name="phone_number"
                                   value="<?php echo htmlspecialchars($contactInfo['phone_number'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                   placeholder="+254 7XX XXX XXX">
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-envelope mr-2 text-orange-500"></i>General Email Address
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="<?php echo htmlspecialchars($contactInfo['email'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                   placeholder="<EMAIL>">
                            <p class="text-sm text-gray-500 mt-1">Used for general inquiries and contact messages</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <!-- Booking Email -->
                        <div>
                            <label for="booking_email" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-check mr-2 text-orange-500"></i>Booking Email Address
                            </label>
                            <input type="email"
                                   id="booking_email"
                                   name="booking_email"
                                   value="<?php echo htmlspecialchars($contactInfo['booking_email'] ?? '<EMAIL>'); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                   placeholder="<EMAIL>">
                            <p class="text-sm text-gray-500 mt-1">Used for quote requests and booking communications</p>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="mt-6">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-orange-500"></i>Business Address
                        </label>
                        <textarea id="address"
                                  name="address"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                  placeholder="Enter your complete business address"><?php echo htmlspecialchars($contactInfo['address'] ?? ''); ?></textarea>
                    </div>

                    <!-- Working Hours -->
                    <div class="mt-6">
                        <label for="working_hours" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-clock mr-2 text-orange-500"></i>Working Hours
                        </label>
                        <input type="text"
                               id="working_hours"
                               name="working_hours"
                               value="<?php echo htmlspecialchars($contactInfo['working_hours'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                               placeholder="Mon-Fri: 9AM-6PM, Sat: 9AM-2PM">
                    </div>

                    <!-- Map Embed Code -->
                    <div class="mt-6">
                        <label for="map_embed_code" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map mr-2 text-orange-500"></i>Google Maps Embed Code
                        </label>
                        <textarea id="map_embed_code"
                                  name="map_embed_code"
                                  rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500 font-mono text-sm"
                                  placeholder="Paste your Google Maps embed iframe code here..."><?php echo htmlspecialchars($contactInfo['map_embed_code'] ?? ''); ?></textarea>
                        <p class="text-sm text-gray-500 mt-1">
                            <i class="fas fa-info-circle mr-1"></i>
                            Get embed code from Google Maps → Share → Embed a map
                        </p>
                    </div>

                    <!-- Social Media Links Section -->
                    <div class="mt-8 border-t border-gray-200 pt-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-share-alt mr-2 text-orange-500"></i>Social Media Links
                        </h4>
                        <p class="text-sm text-gray-600 mb-6">Add your social media profile URLs to display in the website footer</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Facebook -->
                            <div>
                                <label for="facebook_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fa-brands fa-facebook mr-2 text-blue-600"></i>Facebook Page URL
                                </label>
                                <input type="url"
                                       id="facebook_url"
                                       name="facebook_url"
                                       value="<?php echo htmlspecialchars($contactInfo['facebook_url'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="https://facebook.com/yourpage">
                            </div>

                            <!-- Instagram -->
                            <div>
                                <label for="instagram_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fa-brands fa-instagram mr-2 text-pink-600"></i>Instagram Profile URL
                                </label>
                                <input type="url"
                                       id="instagram_url"
                                       name="instagram_url"
                                       value="<?php echo htmlspecialchars($contactInfo['instagram_url'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="https://instagram.com/yourprofile">
                            </div>

                            <!-- Twitter/X -->
                            <div>
                                <label for="twitter_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fa-brands fa-x mr-2 text-black"></i>X (Twitter) Profile URL
                                </label>
                                <input type="url"
                                       id="twitter_url"
                                       name="twitter_url"
                                       value="<?php echo htmlspecialchars($contactInfo['twitter_url'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="https://x.com/yourprofile">
                            </div>

                            <!-- TikTok -->
                            <div>
                                <label for="tiktok_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fa-brands fa-tiktok mr-2 text-black"></i>TikTok Profile URL
                                </label>
                                <input type="url"
                                       id="tiktok_url"
                                       name="tiktok_url"
                                       value="<?php echo htmlspecialchars($contactInfo['tiktok_url'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="https://tiktok.com/@yourprofile">
                            </div>

                            <!-- YouTube -->
                            <div class="md:col-span-2">
                                <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fa-brands fa-youtube mr-2 text-red-600"></i>YouTube Channel URL
                                </label>
                                <input type="url"
                                       id="youtube_url"
                                       name="youtube_url"
                                       value="<?php echo htmlspecialchars($contactInfo['youtube_url'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="https://youtube.com/c/yourchannel">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-8 flex justify-end">
                        <button type="submit"
                                class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>Update Contact Information
                        </button>
                    </div>
                </form>
            </div>

            <!-- Current Information Preview -->
            <div class="mt-8 bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Current Contact Information</h3>
                    <p class="text-sm text-gray-600 mt-1">This is how your contact information appears to visitors</p>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Contact Details -->
                        <div class="space-y-6">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-phone text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Phone</h4>
                                    <p class="text-gray-600"><?php echo htmlspecialchars($contactInfo['phone_number'] ?? 'Not set'); ?></p>
                                    <p class="text-sm text-gray-500">Call us for immediate assistance</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Email</h4>
                                    <p class="text-gray-600"><?php echo htmlspecialchars($contactInfo['email'] ?? 'Not set'); ?></p>
                                    <p class="text-sm text-gray-500">We respond within 24 hours</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Address</h4>
                                    <p class="text-gray-600"><?php echo nl2br(htmlspecialchars($contactInfo['address'] ?? 'Not set')); ?></p>
                                    <p class="text-sm text-gray-500">Visit our office</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Working Hours</h4>
                                    <p class="text-gray-600"><?php echo htmlspecialchars($contactInfo['working_hours'] ?? 'Not set'); ?></p>
                                    <p class="text-sm text-gray-500">Our business hours</p>
                                </div>
                            </div>

                            <!-- Social Media Links -->
                            <div class="border-t border-gray-200 pt-6">
                                <h4 class="font-semibold text-gray-900 mb-4">Social Media</h4>
                                <div class="flex flex-wrap gap-3">
                                    <?php if (!empty($contactInfo['facebook_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($contactInfo['facebook_url']); ?>"
                                           target="_blank"
                                           class="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors">
                                            <i class="fa-brands fa-facebook"></i>
                                            <span class="text-sm">Facebook</span>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($contactInfo['instagram_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($contactInfo['instagram_url']); ?>"
                                           target="_blank"
                                           class="flex items-center space-x-2 bg-pink-50 text-pink-700 px-3 py-2 rounded-lg hover:bg-pink-100 transition-colors">
                                            <i class="fa-brands fa-instagram"></i>
                                            <span class="text-sm">Instagram</span>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($contactInfo['twitter_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($contactInfo['twitter_url']); ?>"
                                           target="_blank"
                                           class="flex items-center space-x-2 bg-gray-50 text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                                            <i class="fa-brands fa-x"></i>
                                            <span class="text-sm">X (Twitter)</span>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($contactInfo['tiktok_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($contactInfo['tiktok_url']); ?>"
                                           target="_blank"
                                           class="flex items-center space-x-2 bg-gray-50 text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                                            <i class="fa-brands fa-tiktok"></i>
                                            <span class="text-sm">TikTok</span>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($contactInfo['youtube_url'])): ?>
                                        <a href="<?php echo htmlspecialchars($contactInfo['youtube_url']); ?>"
                                           target="_blank"
                                           class="flex items-center space-x-2 bg-red-50 text-red-700 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors">
                                            <i class="fa-brands fa-youtube"></i>
                                            <span class="text-sm">YouTube</span>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (empty($contactInfo['facebook_url']) && empty($contactInfo['instagram_url']) &&
                                              empty($contactInfo['twitter_url']) && empty($contactInfo['tiktok_url']) &&
                                              empty($contactInfo['youtube_url'])): ?>
                                        <p class="text-gray-500 text-sm">No social media links configured</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Map Preview -->
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-4">Location Map Preview</h4>
                            <?php if (!empty($contactInfo['map_embed_code'])): ?>
                                <div class="relative">
                                    <!-- Map Container with same styling as frontend -->
                                    <div class="map-container h-96 rounded-2xl overflow-hidden border border-gray-200 shadow-sm">
                                        <?php echo $contactInfo['map_embed_code']; ?>
                                    </div>

                                    <!-- Fallback message if map is blocked -->
                                    <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div class="flex items-start">
                                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                            <div class="text-sm">
                                                <p class="text-blue-800 font-medium">Map Preview</p>
                                                <p class="text-blue-600 mt-1">
                                                    If you see "Content blocked" above, this is normal in the admin panel due to security restrictions.
                                                    The map will display correctly on the frontend contact page.
                                                </p>
                                                <a href="../contact.php" target="_blank" class="text-blue-700 hover:text-blue-800 underline mt-2 inline-block">
                                                    <i class="fas fa-external-link-alt mr-1"></i>View map on frontend
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="h-96 bg-gray-100 rounded-2xl flex items-center justify-center border border-gray-200">
                                    <div class="text-center text-gray-500">
                                        <i class="fas fa-map text-4xl mb-4"></i>
                                        <p class="text-lg font-medium">No map configured</p>
                                        <p class="text-sm mt-2">Add Google Maps embed code above to display your location</p>
                                        <div class="mt-4 text-xs text-gray-400">
                                            <p>Get embed code from: Google Maps → Share → Embed a map</p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Last Updated Info -->
            <?php if (isset($contactInfo['updated_at'])): ?>
                <div class="mt-6 text-sm text-gray-500 text-center">
                    <i class="fas fa-clock mr-1"></i>
                    Last updated: <?php echo date('M j, Y g:i A', strtotime($contactInfo['updated_at'])); ?>
                    <?php if (isset($contactInfo['last_edited_by_username']) && $contactInfo['last_edited_by_username']): ?>
                        by <?php echo htmlspecialchars($contactInfo['last_edited_by_username']); ?>
                    <?php elseif (isset($contactInfo['last_edited_by_user_id'])): ?>
                        by User ID: <?php echo $contactInfo['last_edited_by_user_id']; ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add the sidebar toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('sidebar-open');
                    sidebarOverlay.classList.toggle('active');
                });
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>
