<?php
/**
 * Pesapal IPN (Instant Payment Notification) Handler
 * This file handles instant payment notifications from Pesapal
 */

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/config/pesapal_config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/PesapalService.php';
require_once 'admin-dashboard/classes/EmailService.php';

// Log the IPN for debugging
error_log("Pesapal IPN received: " . json_encode($_GET));
error_log("Pesapal IPN POST data: " . json_encode($_POST));

try {
    // Initialize models and services
    $paymentModel = new Payment();
    $bookingModel = new Booking();
    $quoteModel = new Quote();
    $pesapalService = new PesapalService();
    $emailService = new EmailService();

    // Get parameters from Pesapal IPN
    $orderTrackingId = $_GET['OrderTrackingId'] ?? $_POST['OrderTrackingId'] ?? null;
    $orderMerchantReference = $_GET['OrderMerchantReference'] ?? $_POST['OrderMerchantReference'] ?? null;

    if (!$orderTrackingId || !$orderMerchantReference) {
        error_log("IPN Error: Missing required parameters");
        http_response_code(400);
        echo "Missing required parameters";
        exit;
    }

    error_log("Processing IPN for Order: $orderTrackingId, Merchant Ref: $orderMerchantReference");

    // Get authentication token
    $authToken = $pesapalService->getAuthToken();
    if (!$authToken) {
        error_log("IPN Error: Failed to authenticate with Pesapal");
        http_response_code(500);
        echo "Authentication failed";
        exit;
    }

    // Get transaction status from Pesapal
    $transactionStatus = $pesapalService->getTransactionStatus($authToken, $orderTrackingId);
    
    if (!$transactionStatus) {
        error_log("IPN Error: Failed to get transaction status for $orderTrackingId");
        http_response_code(500);
        echo "Failed to get transaction status";
        exit;
    }

    error_log("Transaction status for $orderTrackingId: " . json_encode($transactionStatus));

    // Find the payment record
    $payment = $paymentModel->findByMerchantReference($orderMerchantReference);
    
    if (!$payment) {
        error_log("IPN Error: Payment not found for merchant reference: $orderMerchantReference");
        http_response_code(404);
        echo "Payment not found";
        exit;
    }

    // Update payment status based on Pesapal response
    $paymentStatus = strtolower($transactionStatus['payment_status_description'] ?? 'unknown');
    $pesapalReference = $transactionStatus['confirmation_code'] ?? null;

    error_log("Updating payment {$payment['payment_id']} with status: $paymentStatus");

    switch ($paymentStatus) {
        case 'completed':
        case 'success':
            // Payment successful
            $now = new DateTime('now', new DateTimeZone(DatabaseConfig::TIMEZONE));
            $updateData = [
                'payment_status' => 'completed',
                'pesapal_tracking_id' => $orderTrackingId,
                'pesapal_reference' => $pesapalReference,
                'payment_date' => $now->format('Y-m-d H:i:s'),
                'updated_at' => $now->format('Y-m-d H:i:s')
            ];
            
            $paymentModel->update($payment['payment_id'], $updateData);
            
            // Update booking or quote status
            if ($payment['booking_id']) {
                // Update booking status
                $bookingModel->update($payment['booking_id'], [
                    'booking_status' => 'confirmed',
                    'payment_status' => 'paid',
                    'updated_at' => $now->format('Y-m-d H:i:s')
                ]);
                
                $booking = $bookingModel->findById($payment['booking_id']);
                error_log("Updated booking {$payment['booking_id']} status to confirmed");
                
                // Send booking confirmation email
                try {
                    $emailService->sendBookingConfirmation($booking);
                    error_log("Booking confirmation email sent for booking {$payment['booking_id']}");
                } catch (Exception $e) {
                    error_log("Failed to send booking confirmation email: " . $e->getMessage());
                }
                
            } elseif ($payment['quote_id']) {
                // Update quote payment status (this calculates total_paid and updates status)
                $quoteModel->updatePaymentStatus($payment['quote_id']);
                
                $quote = $quoteModel->findById($payment['quote_id']);
                error_log("Updated quote {$payment['quote_id']} status to paid");
                
                // Email will be sent by payment-success.php to avoid duplicates
                error_log("Payment processed successfully for quote {$payment['quote_id']}. Email will be sent by payment-success.php");
            }
            
            break;
            
        case 'failed':
        case 'cancelled':
        case 'invalid':
            // Payment failed
            $updateData = [
                'payment_status' => 'failed',
                'pesapal_tracking_id' => $orderTrackingId,
                'pesapal_reference' => $pesapalReference,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $paymentModel->update($payment['payment_id'], $updateData);
            error_log("Payment {$payment['payment_id']} marked as failed");
            break;
            
        case 'pending':
        default:
            // Payment still pending
            $updateData = [
                'payment_status' => 'pending',
                'pesapal_tracking_id' => $orderTrackingId,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $paymentModel->update($payment['payment_id'], $updateData);
            error_log("Payment {$payment['payment_id']} status updated to pending");
            break;
    }

    // Respond to Pesapal
    http_response_code(200);
    echo "IPN processed successfully";
    
} catch (Exception $e) {
    error_log("IPN Error: " . $e->getMessage());
    error_log("IPN Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo "Internal server error";
}
?>
