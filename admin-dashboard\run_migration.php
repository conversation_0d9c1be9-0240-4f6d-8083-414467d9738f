<?php
/**
 * Migration Script: Add Social Media Fields to Contact Info Table
 *
 * This script adds social media URL fields to the contact_info table
 * for existing databases that don't have these fields yet.
 *
 * Run this script once after updating your codebase to add social media functionality.
 */

// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h2>Running Migration: Add Social Media Fields & Booking Email</h2>\n";
    echo "<pre>\n";

    // Check if we need to run the social media migration
    $checkSql = "SHOW COLUMNS FROM contact_info LIKE 'facebook_url'";
    $stmt = $db->prepare($checkSql);
    $stmt->execute();
    $result = $stmt->fetch();

    if ($result) {
        echo "✓ Social media fields already exist in contact_info table.\n";
    } else {
        echo "Adding social media fields to contact_info table...\n\n";

        // Add the social media fields
        $migrations = [
            "ALTER TABLE contact_info ADD COLUMN facebook_url VARCHAR(255) AFTER map_embed_code",
            "ALTER TABLE contact_info ADD COLUMN twitter_url VARCHAR(255) AFTER facebook_url",
            "ALTER TABLE contact_info ADD COLUMN instagram_url VARCHAR(255) AFTER twitter_url",
            "ALTER TABLE contact_info ADD COLUMN tiktok_url VARCHAR(255) AFTER instagram_url",
            "ALTER TABLE contact_info ADD COLUMN youtube_url VARCHAR(255) AFTER tiktok_url"
        ];

        foreach ($migrations as $sql) {
            try {
                $stmt = $db->prepare($sql);
                $stmt->execute();
                echo "✓ " . $sql . "\n";
            } catch (PDOException $e) {
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "  SQL: " . $sql . "\n";
            }
        }

        echo "\n✓ Social media migration completed successfully!\n";
    }

    // Check if we need to add booking_email field
    $checkBookingEmailSql = "SHOW COLUMNS FROM contact_info LIKE 'booking_email'";
    $stmt = $db->prepare($checkBookingEmailSql);
    $stmt->execute();
    $bookingEmailResult = $stmt->fetch();

    if ($bookingEmailResult) {
        echo "✓ booking_email field already exists in contact_info table.\n";
    } else {
        echo "\nAdding booking_email field to contact_info table...\n";

        try {
            $addBookingEmailSql = "ALTER TABLE contact_info ADD COLUMN booking_email VARCHAR(100) DEFAULT '<EMAIL>' AFTER email";
            $stmt = $db->prepare($addBookingEmailSql);
            $stmt->execute();
            echo "✓ " . $addBookingEmailSql . "\n";

            // Update existing records
            $updateSql = "UPDATE contact_info SET booking_email = '<EMAIL>' WHERE booking_email IS NULL";
            $stmt = $db->prepare($updateSql);
            $stmt->execute();
            echo "✓ Updated existing records with default booking email\n";

            echo "\n✓ Booking email migration completed successfully!\n";
        } catch (PDOException $e) {
            echo "✗ Error adding booking_email field: " . $e->getMessage() . "\n";
        }
    }

    echo "\nYou can now manage social media links and booking email in the Contact Info admin page.\n";
    
    // Show final table structure
    echo "\nCurrent contact_info table structure:\n";
    echo "=====================================\n";
    $descSql = "DESCRIBE contact_info";
    $stmt = $db->prepare($descSql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo sprintf("%-20s %-20s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'] === 'YES' ? 'NULL' : 'NOT NULL'
        );
    }
    
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<pre>Error running migration: " . $e->getMessage() . "</pre>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Migration: Add Social Media Fields</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
        h2 { color: #333; }
    </style>
</head>
<body>
    <p><a href="contact-info.php">← Back to Contact Info</a></p>
    <p><strong>Note:</strong> You only need to run this migration once. After running it successfully, you can delete this file.</p>
</body>
</html>
