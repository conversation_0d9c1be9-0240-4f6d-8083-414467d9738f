<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include security middleware
require_once 'includes/security_middleware.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';
require_once 'classes/EmailService.php';

// Initialize models
$quoteModel = new Quote();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle quote actions (simplified system)
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $quoteId = $_POST['quote_id'] ?? null;
    $action = $_POST['action'];

    if ($quoteId) {
        try {
            switch ($action) {
                case 'edit_quote':
                    $newAmount = (float)($_POST['new_amount'] ?? 0);
                    $changeReason = $_POST['change_reason'] ?? '';

                    if ($newAmount > 0) {
                        // Get current quote details before update
                        $currentQuote = $quoteModel->findById($quoteId);
                        $previousAmount = $currentQuote['quoted_amount'];

                        // Update the quote
                        $updateResult = $quoteModel->updateQuoteAmount($quoteId, $newAmount, $changeReason, $currentUser['user_id']);

                        if ($updateResult) {
                            // Send email notification to customer
                            $emailService = new EmailService();
                            $updatedQuote = $quoteModel->findById($quoteId);

                            $changeData = [
                                'previous_amount' => $previousAmount,
                                'new_amount' => $newAmount,
                                'reason' => $changeReason
                            ];

                            $emailSent = $emailService->sendQuoteChangeNotification($updatedQuote, $changeData, $currentUser);

                            if ($emailSent) {
                                $message = 'Quote amount updated successfully and customer has been notified via email.';
                            } else {
                                $message = 'Quote amount updated successfully, but failed to send email notification. Please contact customer manually.';
                            }
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to update quote amount.';
                            $messageType = 'error';
                        }
                    } else {
                        $message = 'Please enter a valid amount.';
                        $messageType = 'error';
                    }
                    break;

                case 'send_quote':
                    $amount = (float)($_POST['quoted_amount'] ?? 0);
                    $quoteDetails = $_POST['quote_details'] ?? '';
                    $includePaymentLink = isset($_POST['include_payment_link']) && $_POST['include_payment_link'] == '1';

                    if ($amount > 0 && !empty($quoteDetails)) {
                        // Update quote status and amount
                        $quoteModel->updateQuoteStatus($quoteId, 'quoted', $amount);

                        // Get quote details for email
                        $quote = $quoteModel->findById($quoteId);

                        if ($quote) {
                            // Initialize email service
                            $emailService = new EmailService();

                            // Prepare quote email data
                            $quoteEmailData = [
                                'quote_id' => $quote['quote_id'],
                                'quote_reference' => $quote['quote_reference'],
                                'customer_name' => $quote['customer_name'],
                                'customer_email' => $quote['customer_email'],
                                'quoted_amount' => $amount,
                                'quote_details' => $quoteDetails,
                                'travel_date' => $quote['travel_date'],
                                'number_of_adults' => $quote['number_of_adults'],
                                'number_of_children' => $quote['number_of_children'],
                                'special_requirements' => $quote['special_requirements'],
                                'include_payment_link' => $includePaymentLink
                            ];

                            // Update quote status to 'quoted' when sending quote
                            $quoteModel->updateQuoteStatus($quote['quote_id'], 'quoted');

                            // Send quote email to customer
                            $emailSent = $emailService->sendQuoteToCustomer($quoteEmailData, $currentUser);

                            if ($emailSent) {
                                $linkText = $includePaymentLink ? ' with payment link' : ' (payment link not included)';
                                $message = 'Quote sent successfully to customer via email' . $linkText . '.';
                                $messageType = 'success';
                            } else {
                                $message = 'Quote updated but failed to send email. Please contact customer manually.';
                                $messageType = 'warning';
                            }
                        }
                    } else {
                        $message = 'Please provide both amount and quote details.';
                        $messageType = 'error';
                    }
                    break;

                case 'reject':
                    $quoteModel->updateQuoteStatus($quoteId, 'rejected');
                    $message = 'Quote rejected successfully.';
                    $messageType = 'success';
                    break;
            }
        } catch (Exception $e) {
            error_log("Quote action error: " . $e->getMessage());
            $message = 'An error occurred while processing the quote.';
            $messageType = 'error';
        }
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$searchFilter = $_GET['search'] ?? '';
$dateFromFilter = $_GET['date_from'] ?? '';
$dateToFilter = $_GET['date_to'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Build filter array
$filters = [];
if ($statusFilter) $filters['status'] = $statusFilter;
if ($searchFilter) $filters['search'] = $searchFilter;
if ($dateFromFilter) $filters['date_from'] = $dateFromFilter;
if ($dateToFilter) $filters['date_to'] = $dateToFilter;

// Fetch quotes with filters (with error handling)
try {
    $quotes = $quoteModel->findAllWithDetails($limit, $offset, $filters);
    $totalQuotes = $quoteModel->countWithFilters($filters);
    $totalPages = ceil($totalQuotes / $limit);
} catch (Exception $e) {
    // Log the error and show user-friendly message
    error_log("Quote search error: " . $e->getMessage());
    error_log("Filters: " . print_r($filters, true));

    // Fallback to show all quotes without filters
    $quotes = $quoteModel->findAllWithDetails($limit, $offset, []);
    $totalQuotes = $quoteModel->countWithFilters([]);
    $totalPages = ceil($totalQuotes / $limit);

    // Set error message for user
    $message = 'Search encountered an error. Showing all quotes instead. Error: ' . $e->getMessage();
    $messageType = 'error';
}

// Calculate pagination
$totalPages = ceil($totalQuotes / $limit);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Quotes - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="mobile-menu-btn" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Manage Quotes</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <div class="max-w-7xl mx-auto">

                <!-- Info Banner -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div class="text-sm text-blue-800">
                            <p>Click the <i class="fas fa-eye mx-1"></i> icon to view detailed customer information including selected packages, activities, and special requirements.</p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php
                        switch($messageType) {
                            case 'success': echo 'bg-green-100 text-green-800'; break;
                            case 'warning': echo 'bg-yellow-100 text-yellow-800'; break;
                            case 'error': echo 'bg-red-100 text-red-800'; break;
                            default: echo 'bg-blue-100 text-blue-800';
                        }
                    ?>">
                        <i class="fas fa-<?php
                            switch($messageType) {
                                case 'success': echo 'check-circle'; break;
                                case 'warning': echo 'exclamation-triangle'; break;
                                case 'error': echo 'times-circle'; break;
                                default: echo 'info-circle';
                            }
                        ?> mr-2"></i><?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <form method="GET" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Status Filter -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                    <option value="">All Statuses</option>
                                    <option value="quoted" <?php echo $statusFilter === 'quoted' ? 'selected' : ''; ?>>Quoted</option>
                                    <option value="accepted" <?php echo $statusFilter === 'accepted' ? 'selected' : ''; ?>>Accepted</option>
                                    <option value="partially_paid" <?php echo $statusFilter === 'partially_paid' ? 'selected' : ''; ?>>Partially Paid</option>
                                    <option value="paid" <?php echo $statusFilter === 'paid' ? 'selected' : ''; ?>>Fully Paid</option>
                                    <option value="rejected" <?php echo $statusFilter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="expired" <?php echo $statusFilter === 'expired' ? 'selected' : ''; ?>>Expired</option>
                                </select>
                            </div>

                            <!-- Search Filter -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input type="text"
                                       name="search"
                                       id="search"
                                       value="<?php echo htmlspecialchars($searchFilter); ?>"
                                       placeholder="Customer name or quote reference"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>

                            <!-- Date From Filter -->
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                                <input type="date"
                                       name="date_from"
                                       id="date_from"
                                       value="<?php echo htmlspecialchars($dateFromFilter); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>

                            <!-- Date To Filter -->
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                                <input type="date"
                                       name="date_to"
                                       id="date_to"
                                       value="<?php echo htmlspecialchars($dateToFilter); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                            <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition duration-300 flex items-center">
                                <i class="fas fa-search mr-2"></i>Apply Filters
                            </button>

                            <a href="quotes.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition duration-300 flex items-center">
                                <i class="fas fa-times mr-2"></i>Clear All
                            </a>

                            <!-- Quick Status Filters -->
                            <div class="flex flex-wrap items-center gap-2 ml-auto">
                                <span class="text-sm text-gray-600 mr-2">Quick filters:</span>
                                <a href="?status=partially_paid" class="inline-flex items-center justify-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-yellow-200 transition duration-300 min-w-[120px]">
                                    Partially Paid
                                </a>
                                <a href="?status=paid" class="inline-flex items-center justify-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-green-200 transition duration-300 min-w-[100px]">
                                    Fully Paid
                                </a>
                                <a href="?status=quoted" class="inline-flex items-center justify-center bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-200 transition duration-300 min-w-[80px]">
                                    Quoted
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Filter Summary -->
                <?php if ($statusFilter || $searchFilter || $dateFromFilter || $dateToFilter): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex flex-wrap items-center gap-3">
                        <span class="text-sm font-medium text-blue-800">
                            <i class="fas fa-filter mr-1"></i>Active Filters:
                        </span>

                        <?php if ($statusFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Status: <?php echo ucfirst(str_replace('_', ' ', $statusFilter)); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['status' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($searchFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Search: "<?php echo htmlspecialchars($searchFilter); ?>"
                            <a href="<?php echo http_build_query(array_merge($_GET, ['search' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($dateFromFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            From: <?php echo htmlspecialchars($dateFromFilter); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['date_from' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($dateToFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            To: <?php echo htmlspecialchars($dateToFilter); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['date_to' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <span class="text-sm text-blue-700 ml-auto">
                            Showing <?php echo count($quotes); ?> of <?php echo $totalQuotes; ?> quotes
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Quotes Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quote Ref</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Travel</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($quotes)): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                            <i class="fas fa-file-invoice text-4xl mb-4"></i>
                                            <p class="text-lg">No quotes found</p>
                                            <p class="text-sm">Quote requests will appear here when customers submit them</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($quotes as $quote): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($quote['quote_reference']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($quote['created_at'])); ?></div>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($quote['customer_name']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($quote['customer_email']); ?></div>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm text-gray-900">
                                                        <?php
                                                        if ($quote['travel_date'] && $quote['travel_date'] !== '0000-00-00' && $quote['travel_date'] !== '1970-01-01') {
                                                            // Format travel date using local timezone
                                                            $travelDate = new DateTime($quote['travel_date'], new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                            echo $travelDate->format('M j, Y');
                                                        } else {
                                                            echo '<span class="text-gray-400">Flexible</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        <?php echo $quote['number_of_adults'] + $quote['number_of_children']; ?> travelers
                                                    </div>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <?php if ($quote['quoted_amount']): ?>
                                                        <div class="text-sm font-medium text-gray-900">$<?php echo number_format($quote['quoted_amount'], 2); ?></div>
                                                        <?php if ($quote['expires_at']): ?>
                                                            <div class="text-xs text-gray-500">Expires: <?php echo date('M j, Y', strtotime($quote['expires_at'])); ?></div>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-sm text-gray-500">Not quoted</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    <?php
                                                    switch($quote['quote_status']) {
                                                        case 'quoted': echo 'bg-blue-100 text-blue-800'; break;
                                                        case 'accepted': echo 'bg-blue-100 text-blue-800'; break;
                                                        case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                        case 'expired': echo 'bg-gray-100 text-gray-800'; break;
                                                        case 'paid': echo 'bg-green-100 text-green-800'; break;
                                                        case 'partially_paid': echo 'bg-yellow-100 text-yellow-800'; break;
                                                        default: echo 'bg-purple-100 text-purple-800';
                                                    }
                                                    ?>">
                                                    <?php
                                                    switch($quote['quote_status']) {
                                                        case 'partially_paid': echo 'Partially Paid'; break;
                                                        case 'paid': echo 'Fully Paid'; break;
                                                        case 'quoted': echo 'Quoted'; break;
                                                        case 'accepted': echo 'Accepted'; break;
                                                        case 'rejected': echo 'Rejected'; break;
                                                        case 'expired': echo 'Expired'; break;
                                                        default: echo ucfirst($quote['quote_status']);
                                                    }
                                                    ?>
                                                </span>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <!-- View Details Button -->
                                                    <button onclick="openDetailsModal(<?php echo $quote['quote_id']; ?>)"
                                                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md
                                                            <?php
                                                            switch($quote['quote_status']) {
                                                                case 'quoted': echo 'bg-blue-100 text-blue-800 hover:bg-blue-200'; break;
                                                                case 'accepted': echo 'bg-blue-100 text-blue-800 hover:bg-blue-200'; break;
                                                                case 'rejected': echo 'bg-red-100 text-red-800 hover:bg-red-200'; break;
                                                                case 'expired': echo 'bg-gray-100 text-gray-800 hover:bg-gray-200'; break;
                                                                case 'paid': echo 'bg-green-100 text-green-800 hover:bg-green-200'; break;
                                                                case 'partially_paid': echo 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'; break;
                                                                default: echo 'bg-purple-100 text-purple-800 hover:bg-purple-200';
                                                            }
                                                            ?>"
                                                            title="View Details">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        <span>Details</span>
                                                    </button>

                                                    <?php if ($quote['quote_status'] === 'pending'): ?>
                                                        <button onclick="openQuoteModal(<?php echo $quote['quote_id']; ?>, '<?php echo htmlspecialchars($quote['quote_reference']); ?>')"
                                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md bg-purple-100 text-purple-800 hover:bg-purple-200"
                                                                title="Send Quote">
                                                            <i class="fas fa-file-invoice-dollar mr-1"></i>
                                                            <span>Send Quote</span>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if (in_array($quote['quote_status'], ['quoted', 'accepted', 'partially_paid']) && $quote['quoted_amount']): ?>
                                                        <button onclick="openEditQuoteModal(<?php echo $quote['quote_id']; ?>, '<?php echo htmlspecialchars($quote['quote_reference']); ?>', <?php echo $quote['quoted_amount']; ?>)"
                                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md bg-orange-100 text-orange-800 hover:bg-orange-200"
                                                                title="Edit Quote Amount">
                                                            <i class="fas fa-edit mr-1"></i>
                                                            <span>Edit</span>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if ($quote['quote_status'] === 'quoted'): ?>
                                                        <button onclick="copyPaymentLink('<?php echo urlencode($quote['quote_reference']); ?>')"
                                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md bg-blue-100 text-blue-800 hover:bg-blue-200"
                                                                title="Copy Payment Link">
                                                            <i class="fas fa-link mr-1"></i>
                                                            <span>Copy Link</span>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if ($quote['quote_status'] === 'accepted'): ?>
                                                        <button onclick="copyPaymentLink('<?php echo urlencode($quote['quote_reference']); ?>')"
                                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md bg-blue-100 text-blue-800 hover:bg-blue-200"
                                                                title="Copy Payment Link">
                                                            <i class="fas fa-link mr-1"></i>
                                                            <span>Copy Link</span>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if (in_array($quote['quote_status'], ['paid', 'partially_paid'])): ?>
                                                        <button onclick="openPaymentHistoryModal(<?php echo $quote['quote_id']; ?>)"
                                                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition duration-300 hover:shadow-md
                                                                <?php
                                                                switch($quote['quote_status']) {
                                                                    case 'paid': echo 'bg-green-100 text-green-800 hover:bg-green-200'; break;
                                                                    case 'partially_paid': echo 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'; break;
                                                                    default: echo 'bg-purple-100 text-purple-800 hover:bg-purple-200';
                                                                }
                                                                ?>"
                                                                title="View Payment History">
                                                            <i class="fas fa-money-bill-wave mr-1"></i>
                                                            <span>Payments</span>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($totalQuotes > $limit): ?>
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium"><?php echo $offset + 1; ?></span> to
                                    <span class="font-medium"><?php echo min($offset + $limit, $totalQuotes); ?></span> of
                                    <span class="font-medium"><?php echo $totalQuotes; ?></span> quotes
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <?php if ($page > 1): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    if ($startPage > 1): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                        <?php if ($startPage > 2): ?>
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                        <?php if ($i == $page): ?>
                                            <span class="relative inline-flex items-center px-4 py-2 border border-orange-500 bg-orange-50 text-sm font-medium text-orange-600"><?php echo $i; ?></span>
                                        <?php else: ?>
                                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $i; ?></a>
                                        <?php endif; ?>
                                    <?php endfor; ?>

                                    <?php if ($endPage < $totalPages): ?>
                                        <?php if ($endPage < $totalPages - 1): ?>
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                        <?php endif; ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>"
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $totalPages; ?></a>
                                    <?php endif; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Send Quote Modal -->
    <div id="quoteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-1 h-8 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full mr-4"></div>
                            <h3 class="text-lg font-medium text-gray-900">Send Quote to Customer</h3>
                        </div>
                        <button onclick="closeQuoteModal()" class="text-gray-400 hover:text-gray-600 transition duration-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form method="POST">
                        <input type="hidden" name="quote_id" id="modalQuoteId">
                        <input type="hidden" name="action" value="send_quote">
                        <?php echo csrf_field(); ?>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Quote Reference</label>
                            <input type="text" id="modalQuoteRef" readonly class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Total Amount (USD) <span class="text-red-500">*</span></label>
                            <input type="number" name="quoted_amount" step="0.01" min="0" required
                                   placeholder="e.g., 1500.00"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Enter the total amount for the entire trip</p>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Quote Details <span class="text-red-500">*</span></label>
                            <textarea name="quote_details" rows="8" required
                                      placeholder="Provide detailed information about the quote including:&#10;- Itinerary overview&#10;- Accommodation details&#10;- Transportation&#10;- Included activities&#10;- What's included/excluded&#10;- Terms and conditions"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"></textarea>
                            <p class="text-xs text-gray-500 mt-1">This will be included in the email sent to the customer</p>
                        </div>

                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" name="include_payment_link" id="includePaymentLink" value="1"
                                       class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                <label for="includePaymentLink" class="ml-2 block text-sm text-gray-700">
                                    Include payment link in email
                                </label>
                            </div>
                            <p class="text-xs text-gray-500 mt-1 ml-6">Check this only after you've agreed on the amount with the customer</p>
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                                <div class="text-sm text-blue-800">
                                    <p class="font-medium mb-1">What happens next:</p>
                                    <ul class="list-disc list-inside space-y-1 text-xs">
                                        <li>Customer receives detailed quote via email</li>
                                        <li id="paymentLinkInfo" class="hidden">Email includes a secure payment link</li>
                                        <li id="noPaymentLinkInfo">You can include payment link later after agreement</li>
                                        <li id="paymentDirectInfo" class="hidden">Customer can pay directly using the link</li>
                                        <li>You'll be notified when payment is completed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeQuoteModal()"
                                    class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 flex items-center">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Quote via Email
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quote Details Modal -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-1 h-8 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full mr-4"></div>
                            <h3 class="text-lg font-medium text-gray-900">Quote Request Details</h3>
                        </div>
                        <button onclick="closeDetailsModal()" class="text-gray-400 hover:text-gray-600 transition duration-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div id="detailsContent">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .sidebar-overlay { position: fixed; top: 0; left: 0; w-full; h-full; background: rgba(0, 0, 0, 0.5); z-index: 40; display: none; }
        .main-content { transition: margin-left 0.3s ease-in-out; }
        .sidebar-active { background-color: rgba(249, 115, 22, 0.1); border-right: 3px solid #f97316; }
        
        @media (max-width: 1024px) {
            #sidebar { transform: translateX(-100%); }
            #sidebar.show { transform: translateX(0); }
            .sidebar-overlay.show { display: block; }
        }
    </style>

    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                });
            }
        });

        // Quote modal functions
        function openQuoteModal(quoteId, quoteRef) {
            document.getElementById('modalQuoteId').value = quoteId;
            document.getElementById('modalQuoteRef').value = quoteRef;
            document.getElementById('quoteModal').classList.remove('hidden');
        }

        function closeQuoteModal() {
            document.getElementById('quoteModal').classList.add('hidden');
        }

        // Details modal functions
        function openDetailsModal(quoteId) {
            // Fetch quote details via AJAX
            fetch('get-quote-details.php?id=' + quoteId)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('detailsContent').innerHTML = html;
                    document.getElementById('detailsModal').classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error fetching quote details:', error);
                    alert('Error loading quote details');
                });
        }

        function closeDetailsModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        // Payment history modal functions
        function openPaymentHistoryModal(quoteId) {
            // Fetch payment history via AJAX
            fetch('get-payment-history.php?quote_id=' + quoteId)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('paymentHistoryContent').innerHTML = html;
                    document.getElementById('paymentHistoryModal').classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error fetching payment history:', error);
                    alert('Error loading payment history');
                });
        }

        function closePaymentHistoryModal() {
            document.getElementById('paymentHistoryModal').classList.add('hidden');
        }

        // Enhanced Filter Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const statusSelect = document.getElementById('status');
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');

            // Auto-submit on status change
            if (statusSelect) {
                statusSelect.addEventListener('change', function() {
                    if (this.value) {
                        this.form.submit();
                    }
                });
            }

            // Search on Enter key
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.form.submit();
                    }
                });
            }

            // Auto-submit when date range is complete
            function checkDateRange() {
                if (dateFromInput && dateToInput && dateFromInput.value && dateToInput.value) {
                    dateFromInput.form.submit();
                }
            }

            if (dateFromInput) dateFromInput.addEventListener('change', checkDateRange);
            if (dateToInput) dateToInput.addEventListener('change', checkDateRange);

            // Highlight search terms in results
            const searchTerm = '<?php echo addslashes($searchFilter); ?>';
            if (searchTerm) {
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                document.querySelectorAll('td').forEach(cell => {
                    if (cell.textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                        cell.innerHTML = cell.innerHTML.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
                    }
                });
            }
        });

        // Copy payment link function
        function copyPaymentLink(quoteRef) {
            // Smart URL detection for localhost vs live server
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const basePath = isLocalhost ? '/meleva' : '';
            const paymentUrl = window.location.origin + basePath + '/payment.php?quote_ref=' + quoteRef;

            // Use the modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(paymentUrl).then(() => {
                    showToast('Payment link copied to clipboard!', 'success');
                }).catch(() => {
                    fallbackCopyTextToClipboard(paymentUrl);
                });
            } else {
                fallbackCopyTextToClipboard(paymentUrl);
            }
        }

        // Fallback for older browsers
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast('Payment link copied to clipboard!', 'success');
            } catch (err) {
                showToast('Failed to copy link. Please copy manually: ' + text, 'error');
            }

            document.body.removeChild(textArea);
        }

        // Handle payment link checkbox
        document.addEventListener('DOMContentLoaded', function() {
            const paymentLinkCheckbox = document.getElementById('includePaymentLink');
            const paymentLinkInfo = document.getElementById('paymentLinkInfo');
            const noPaymentLinkInfo = document.getElementById('noPaymentLinkInfo');
            const paymentDirectInfo = document.getElementById('paymentDirectInfo');

            if (paymentLinkCheckbox) {
                paymentLinkCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        paymentLinkInfo.classList.remove('hidden');
                        paymentDirectInfo.classList.remove('hidden');
                        noPaymentLinkInfo.classList.add('hidden');
                    } else {
                        paymentLinkInfo.classList.add('hidden');
                        paymentDirectInfo.classList.add('hidden');
                        noPaymentLinkInfo.classList.remove('hidden');
                    }
                });
            }
        });

        // Simple toast notification
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white font-medium z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Fade out after 3 seconds
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
        // Edit Quote Modal Functions
        function openEditQuoteModal(quoteId, quoteRef, currentAmount) {
            document.getElementById('editQuoteId').value = quoteId;
            document.getElementById('editQuoteRef').textContent = quoteRef;
            document.getElementById('editCurrentAmount').textContent = '$' + parseFloat(currentAmount).toFixed(2);
            document.getElementById('newAmount').value = parseFloat(currentAmount).toFixed(2);
            document.getElementById('changeReason').value = '';
            document.getElementById('editQuoteModal').classList.remove('hidden');
        }

        function closeEditQuoteModal() {
            document.getElementById('editQuoteModal').classList.add('hidden');
        }

        function submitEditQuote() {
            const form = document.getElementById('editQuoteForm');
            const newAmount = parseFloat(document.getElementById('newAmount').value);
            const changeReason = document.getElementById('changeReason').value.trim();

            if (newAmount <= 0) {
                showToast('Please enter a valid amount', 'error');
                return;
            }

            if (!changeReason) {
                showToast('Please provide a reason for the change', 'error');
                return;
            }

            form.submit();
        }
    </script>

    <!-- Edit Quote Modal -->
    <div id="editQuoteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-lg bg-white">
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-1 h-8 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full mr-4"></div>
                    <h3 class="text-lg font-bold text-gray-900">Edit Quote Amount</h3>
                </div>
                <button onclick="closeEditQuoteModal()" class="text-gray-400 hover:text-gray-600 transition duration-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="editQuoteForm" method="POST" action="">
                <input type="hidden" name="action" value="edit_quote">
                <input type="hidden" id="editQuoteId" name="quote_id" value="">
                <?php echo csrf_field(); ?>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Quote Reference</label>
                    <div class="text-lg font-semibold text-gray-900" id="editQuoteRef"></div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Current Amount</label>
                    <div class="text-lg font-semibold text-green-600" id="editCurrentAmount"></div>
                </div>

                <div class="mb-4">
                    <label for="newAmount" class="block text-sm font-medium text-gray-700 mb-2">New Amount ($)</label>
                    <input type="number" id="newAmount" name="new_amount" step="0.01" min="0.01"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                           placeholder="Enter new amount" required>
                </div>

                <div class="mb-6">
                    <label for="changeReason" class="block text-sm font-medium text-gray-700 mb-2">Reason for Change</label>
                    <textarea id="changeReason" name="change_reason" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                              placeholder="Explain why the quote amount is being changed..." required></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeEditQuoteModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-300">
                        Cancel
                    </button>
                    <button type="button" onclick="submitEditQuote()"
                            class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition duration-300">
                        <i class="fas fa-save mr-2"></i>Update Quote
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Payment History Modal -->
    <div id="paymentHistoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-lg bg-white">
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-1 h-8 bg-gradient-to-b from-purple-400 to-purple-600 rounded-full mr-4"></div>
                    <h3 class="text-lg font-bold text-gray-900">Payment History</h3>
                </div>
                <button onclick="closePaymentHistoryModal()" class="text-gray-400 hover:text-gray-600 transition duration-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="paymentHistoryContent">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                    <p class="text-gray-500 mt-2">Loading payment history...</p>
                </div>
            </div>
        </div>
    </div>

    <?php injectAutoLogoutScript(); ?>
</body>
</html>
