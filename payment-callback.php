<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/PesapalService.php';
require_once 'admin-dashboard/classes/EmailService.php';

// Initialize models and services
$paymentModel = new Payment();
$bookingModel = new Booking();
$quoteModel = new Quote();
$pesapalService = new PesapalService();
$emailService = new EmailService();

// Log the callback for debugging
error_log("Pesapal callback received: " . json_encode($_GET));
error_log("Pesapal callback POST data: " . json_encode($_POST));
error_log("Pesapal callback headers: " . json_encode(getallheaders()));

// Get parameters from Pesapal callback
$orderTrackingId = $_GET['OrderTrackingId'] ?? null;
$orderMerchantReference = $_GET['OrderMerchantReference'] ?? null;

if (!$orderTrackingId || !$orderMerchantReference) {
    error_log("Missing required parameters in Pesapal callback");
    http_response_code(400);
    echo "Missing required parameters";
    exit;
}

try {
    // Find payment by Pesapal tracking ID
    $payment = $paymentModel->findByPesapalTrackingId($orderTrackingId);
    
    if (!$payment) {
        error_log("Payment not found for tracking ID: " . $orderTrackingId);
        http_response_code(404);
        echo "Payment not found";
        exit;
    }
    
    // Get auth token
    $authToken = $pesapalService->getAuthToken();
    
    if (!$authToken) {
        error_log("Failed to get Pesapal auth token");
        http_response_code(500);
        echo "Authentication failed";
        exit;
    }
    
    // Validate the IPN with Pesapal
    $validationResult = $pesapalService->validateIPN($authToken, $orderTrackingId, $orderMerchantReference);
    
    if (!$validationResult) {
        error_log("IPN validation failed for tracking ID: " . $orderTrackingId);
        http_response_code(400);
        echo "IPN validation failed";
        exit;
    }
    
    // Get transaction status
    $transactionStatus = $pesapalService->getTransactionStatus($authToken, $orderTrackingId);
    
    if (!$transactionStatus) {
        error_log("Failed to get transaction status for tracking ID: " . $orderTrackingId);
        http_response_code(500);
        echo "Failed to get transaction status";
        exit;
    }
    
    // Map Pesapal status to our status
    $pesapalStatusCode = $transactionStatus['payment_status_description'] ?? '';
    $newStatus = 'pending'; // Default

    // Log the full transaction status for debugging
    error_log("Full Pesapal transaction status: " . json_encode($transactionStatus));
    error_log("Pesapal status code: " . $pesapalStatusCode);

    switch (strtolower($pesapalStatusCode)) {
        case 'completed':
        case 'success':
            $newStatus = 'completed';
            break;
        case 'failed':
        case 'invalid':
            $newStatus = 'failed';
            break;
        case 'cancelled':
            $newStatus = 'cancelled';
            break;
        default:
            $newStatus = 'pending';
    }

    error_log("Mapped status from '$pesapalStatusCode' to '$newStatus'");
    
    // Update payment status
    $paymentModel->updatePaymentStatus(
        $payment['payment_id'], 
        $newStatus, 
        $pesapalStatusCode,
        $transactionStatus['payment_method'] ?? null
    );
    
    // Handle payment completion for both booking-based and quote-based payments
    if ($newStatus === 'completed') {
        try {
            if ($payment['booking_id']) {
                // Handle traditional booking-based payment
                $booking = $bookingModel->findById($payment['booking_id']);
                if ($booking) {
                    // Calculate total paid amount
                    $totalPaid = $paymentModel->getTotalPaidForBooking($payment['booking_id']);

                    // Update booking payment status
                    if ($totalPaid >= $booking['total_amount']) {
                        $bookingModel->updateStatus($payment['booking_id'], 'confirmed', 'paid');
                    } else {
                        $bookingModel->updateStatus($payment['booking_id'], 'confirmed', 'partial');
                    }

                    // Mark that payment receipt will be sent by payment-success.php to avoid duplicates
                    session_start();
                    $_SESSION['payment_receipt_sent_booking_' . $booking['booking_id']] = true;
                    error_log("Payment receipt will be handled by payment-success.php for booking {$booking['booking_id']}");
                }
            } elseif ($payment['quote_id']) {
                // Handle simplified quote-based payment
                $quote = $quoteModel->findById($payment['quote_id']);
                if ($quote) {
                    // Update quote payment status (this calculates total_paid and updates status)
                    $quoteModel->updatePaymentStatus($payment['quote_id']);

                    // Send receipt email for quote-based payment
                    $receiptData = [
                        'customer_name' => $quote['customer_name'],
                        'customer_email' => $quote['customer_email'],
                        'quote_reference' => $quote['quote_reference'],
                        'payment_reference' => $payment['payment_reference'],
                        'amount_paid' => $payment['amount'],
                        'quoted_amount' => $quote['quoted_amount'],
                        'travel_date' => $quote['travel_date'],
                        'number_of_adults' => $quote['number_of_adults'],
                        'number_of_children' => $quote['number_of_children'],
                        'special_requirements' => $quote['special_requirements'],
                        'payment_method' => $transactionStatus['payment_method'] ?? 'Online Payment'
                    ];

                    // Email will be sent by payment-success.php to avoid duplicates
                    error_log("Payment processed successfully for quote: " . $quote['quote_reference'] . ". Email will be sent by payment-success.php");
                }
            }
        } catch (Exception $e) {
            error_log("Error processing payment completion: " . $e->getMessage());
        }
    }
    
    // Log successful processing
    error_log("Payment callback processed successfully for payment ID: " . $payment['payment_id'] . ", Status: " . $newStatus);
    
    // Ensure database consistency before redirect
    sleep(1); // 1 second delay to ensure all updates are committed

    // Redirect to success page
    if ($payment['booking_id']) {
        header("Location: payment-success.php?payment_id=" . $payment['payment_id']);
    } elseif ($payment['quote_id']) {
        // Fetch fresh quote data after update
        $updatedQuote = $quoteModel->findById($payment['quote_id']);
        header("Location: payment-success.php?quote_ref=" . urlencode($updatedQuote['quote_reference']));
    } else {
        header("Location: payment-success.php?payment_id=" . $payment['payment_id']);
    }
    exit;
    
} catch (Exception $e) {
    error_log("Payment callback error: " . $e->getMessage());
    http_response_code(500);
    echo "Internal server error";
}
?>
