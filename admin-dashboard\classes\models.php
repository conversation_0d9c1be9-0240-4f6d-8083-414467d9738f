<?php
require_once __DIR__ . '/../config/config.php';

/**
 * Base Model Class
 */
abstract class BaseModel {
    protected $db;
    protected $table;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function findAll($limit = null, $offset = 0) {
        $sql = "SELECT * FROM {$this->table}";
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
        }
        
        $stmt = $this->db->prepare($sql);
        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function findById($id) {
        $primaryKey = $this->getPrimaryKey();
        $sql = "SELECT * FROM {$this->table} WHERE {$primaryKey} = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function count() {
        $sql = "SELECT COUNT(*) as total FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch()['total'];
    }
    
    public function delete($id) {
        $primaryKey = $this->getPrimaryKey();
        $sql = "DELETE FROM {$this->table} WHERE {$primaryKey} = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    /**
     * Generic create method for BaseModel
     */
    public function create($data) {
        $columns = array_keys($data);
        $placeholders = ':' . implode(', :', $columns);

        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES ({$placeholders})";

        $stmt = $this->db->prepare($sql);

        foreach ($data as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * Check if a column exists in a table
     */
    protected function checkColumnExists($tableName, $columnName) {
        try {
            $sql = "SHOW COLUMNS FROM {$tableName} LIKE :column_name";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':column_name', $columnName);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Generic update method for BaseModel
     */
    public function update($id, $data) {
        $primaryKey = $this->getPrimaryKey();
        $setClause = [];

        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }

        $sql = "UPDATE {$this->table} SET " . implode(', ', $setClause) . " WHERE {$primaryKey} = :id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);

        foreach ($data as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        return $stmt->execute();
    }

    /**
     * Generic find method for BaseModel
     */
    public function find($id) {
        $primaryKey = $this->getPrimaryKey();
        $sql = "SELECT * FROM {$this->table} WHERE {$primaryKey} = :id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    abstract protected function getPrimaryKey();
}

/**
 * User Model
 */
class User extends BaseModel {
    protected $table = 'users';
    
    protected function getPrimaryKey() {
        return 'user_id';
    }
    
   public function authenticate($username, $password) {
    try {
        $sql = "SELECT * FROM users WHERE username = :username OR email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $username);
        $stmt->execute();
        
        $user = $stmt->fetch();
        if (!$user) {
            error_log("No user found for username/email: $username");
            return false;
        }
        
        error_log("Found user: " . print_r($user, true));
        $passwordMatch = password_verify($password, $user['password_hash']);
        error_log("Password match: " . ($passwordMatch ? 'true' : 'false'));
        
        if ($passwordMatch) {
            return $user;
        }
        return false;
    } catch (PDOException $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
}
    
    public function create($data) {
        $sql = "INSERT INTO users (username, password_hash, email, role) VALUES (:username, :password_hash, :email, :role)";
        $stmt = $this->db->prepare($sql);
        
        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $stmt->bindParam(':username', $data['username']);
        $stmt->bindParam(':password_hash', $passwordHash);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':role', $data['role']);
        
        return $stmt->execute();
    }
    
    public function update($id, $data) {
        $sql = "UPDATE users SET username = :username, email = :email, role = :role";
        if (!empty($data['password'])) {
            $sql .= ", password_hash = :password_hash";
        }
        $sql .= " WHERE user_id = :id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':username', $data['username']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':role', $data['role']);
        $stmt->bindParam(':id', $id);
        
        if (!empty($data['password'])) {
            $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
            $stmt->bindParam(':password_hash', $passwordHash);
        }
        
        return $stmt->execute();
    }
    /**
 * Get user by email address
 */
public function getUserByEmail($email) {
    try {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting user by email: " . $e->getMessage());
        return false;
    }
}

/**
 * Create password reset token
 */
public function createPasswordResetToken($userId, $token, $expiresAt) {
    try {
        // First, delete any existing tokens for this user
        $stmt = $this->db->prepare("DELETE FROM password_resets WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // Insert new token
        $stmt = $this->db->prepare("INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)");
        return $stmt->execute([$userId, $token, $expiresAt]);
    } catch (PDOException $e) {
        error_log("Error creating password reset token: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate password reset token
 */
public function validatePasswordResetToken($token) {
    try {
        $stmt = $this->db->prepare("
            SELECT pr.*, u.email, u.username 
            FROM password_resets pr 
            JOIN users u ON pr.user_id = u.user_id 
            WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = FALSE
        ");
        $stmt->execute([$token]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error validating password reset token: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user password
 */
public function updatePassword($userId, $hashedPassword) {
    try {
        $stmt = $this->db->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE user_id = ?");
        return $stmt->execute([$hashedPassword, $userId]);
    } catch (PDOException $e) {
        error_log("Error updating password: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark password reset token as used
 */
public function markTokenAsUsed($token) {
    try {
        $stmt = $this->db->prepare("UPDATE password_resets SET used = TRUE WHERE token = ?");
        return $stmt->execute([$token]);
    } catch (PDOException $e) {
        error_log("Error marking token as used: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean up expired tokens (optional - can be run as a cron job)
 */
public function cleanupExpiredTokens() {
    try {
        $stmt = $this->db->prepare("DELETE FROM password_resets WHERE expires_at < NOW() OR used = TRUE");
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Error cleaning up expired tokens: " . $e->getMessage());
        return false;
    }
}

}

/**
 * Destination Model
 */
class Destination extends BaseModel {
    protected $table = 'destinations';
    
    protected function getPrimaryKey() {
        return 'destination_id';
    }
    
    public function findAllWithImages() {
        $sql = "SELECT d.*, i.url as display_image_url, u.username as created_by
                FROM destinations d
                LEFT JOIN images i ON d.display_image_id = i.image_id
                LEFT JOIN users u ON d.created_by_user_id = u.user_id
                ORDER BY d.created_at ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function findByName($name) {
        $sql = "SELECT * FROM {$this->table} WHERE name = :name";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':name', $name);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function create($data) {
        // Check if destination name already exists
        if ($this->findByName($data['name'])) {
            throw new Exception("A destination with the name '{$data['name']}' already exists. Please choose a different name.");
        }

        $sql = "INSERT INTO destinations (name, location, price, short_description, full_description, created_by_user_id)
                VALUES (:name, :location, :price, :short_description, :full_description, :created_by_user_id)";
        $stmt = $this->db->prepare($sql);

        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':location', $data['location']);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':short_description', $data['short_description']);
        $stmt->bindParam(':full_description', $data['full_description']);
        $stmt->bindParam(':created_by_user_id', $data['created_by_user_id']);

        try {
            if ($stmt->execute()) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            // Handle duplicate entry error specifically
            if ($e->getCode() == 23000) {
                throw new Exception("A destination with this name already exists. Please choose a different name.");
            }
            throw $e;
        }
    }
    
    public function getLastInsertedId() {
        return $this->db->lastInsertId();
    }

    public function update($id, $data) {
        // Check if destination name already exists (excluding current record)
        if (isset($data['name'])) {
            $existing = $this->findByName($data['name']);
            if ($existing && intval($existing['destination_id']) != intval($id)) {
                throw new Exception("A destination with the name '{$data['name']}' already exists. Please choose a different name.");
            }
        }

        // Build dynamic SQL based on provided data
        $fields = [];
        $params = [':id' => $id];

        if (isset($data['name'])) {
            $fields[] = 'name = :name';
            $params[':name'] = $data['name'];
        }
        if (isset($data['location'])) {
            $fields[] = 'location = :location';
            $params[':location'] = $data['location'];
        }
        if (isset($data['price'])) {
            $fields[] = 'price = :price';
            $params[':price'] = $data['price'];
        }
        if (isset($data['short_description'])) {
            $fields[] = 'short_description = :short_description';
            $params[':short_description'] = $data['short_description'];
        }
        if (isset($data['full_description'])) {
            $fields[] = 'full_description = :full_description';
            $params[':full_description'] = $data['full_description'];
        }
        if (isset($data['display_image_id'])) {
            $fields[] = 'display_image_id = :display_image_id';
            $params[':display_image_id'] = $data['display_image_id'];
        }

        if (empty($fields)) {
            return true; // Nothing to update
        }

        $sql = "UPDATE destinations SET " . implode(', ', $fields) . " WHERE destination_id = :id";
        $stmt = $this->db->prepare($sql);

        try {
            return $stmt->execute($params);
        } catch (PDOException $e) {
            // Handle duplicate entry error specifically
            if ($e->getCode() == 23000) {
                throw new Exception("A destination with this name already exists. Please choose a different name.");
            }
            throw $e;
        }
    }
}

/**
 * Tour Package Type Model
 */
class TourPackageType extends BaseModel {
    protected $table = 'tour_package_types';
    
    protected function getPrimaryKey() {
        return 'package_type_id';
    }
    
    public function create($data) {
        $sql = "INSERT INTO tour_package_types (type_name, description) VALUES (:type_name, :description)";
        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':type_name', $data['type_name']);
        $stmt->bindParam(':description', $data['description']);
        
        return $stmt->execute();
    }
    
    public function update($id, $data) {
        $sql = "UPDATE tour_package_types SET type_name = :type_name, description = :description WHERE package_type_id = :id";
        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':type_name', $data['type_name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
}

/**
 * Tour Package Model
 */
class TourPackage extends BaseModel {
    protected $table = 'tour_packages';
    
    protected function getPrimaryKey() {
        return 'tour_package_id';
    }
    
    public function findAllWithDetails() {
        $sql = "SELECT tp.*, tpt.type_name, i.url as display_image_url, u.username as created_by
                FROM tour_packages tp
                LEFT JOIN tour_package_types tpt ON tp.package_type_id = tpt.package_type_id
                LEFT JOIN images i ON tp.display_image_id = i.image_id
                LEFT JOIN users u ON tp.created_by_user_id = u.user_id
                ORDER BY tp.created_at ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function findFeaturedWithDetails($limit = 7) {
        $sql = "SELECT tp.*, tpt.type_name, i.url as display_image_url, u.username as created_by
                FROM tour_packages tp
                LEFT JOIN tour_package_types tpt ON tp.package_type_id = tpt.package_type_id
                LEFT JOIN images i ON tp.display_image_id = i.image_id
                LEFT JOIN users u ON tp.created_by_user_id = u.user_id
                WHERE tp.is_featured = 1
                ORDER BY tp.created_at ASC
                LIMIT :limit";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function findByIdWithDetails($id) {
        $sql = "SELECT tp.*, tpt.type_name, i.url as display_image_url, u.username as created_by
                FROM tour_packages tp
                LEFT JOIN tour_package_types tpt ON tp.package_type_id = tpt.package_type_id
                LEFT JOIN images i ON tp.display_image_id = i.image_id
                LEFT JOIN users u ON tp.created_by_user_id = u.user_id
                WHERE tp.tour_package_id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function findByDestination($destinationId) {
        $sql = "SELECT tp.*, tpt.type_name, i.url as display_image_url, u.username as created_by
                FROM tour_packages tp
                LEFT JOIN tour_package_types tpt ON tp.package_type_id = tpt.package_type_id
                LEFT JOIN images i ON tp.display_image_id = i.image_id
                LEFT JOIN users u ON tp.created_by_user_id = u.user_id
                WHERE tp.destination_id = :destination_id
                ORDER BY tp.created_at ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':destination_id', $destinationId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        $sql = "INSERT INTO tour_packages (package_type_id, destination_id, name, description, price, duration, is_featured, created_by_user_id)
                VALUES (:package_type_id, :destination_id, :name, :description, :price, :duration, :is_featured, :created_by_user_id)";
        $stmt = $this->db->prepare($sql);

        $stmt->bindParam(':package_type_id', $data['package_type_id']);
        $destinationId = !empty($data['destination_id']) ? $data['destination_id'] : null;
        $stmt->bindParam(':destination_id', $destinationId);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':price', $data['price']);
        // Allow empty duration for package types where it's implied
        $duration = !empty($data['duration']) ? $data['duration'] : null;
        $stmt->bindParam(':duration', $duration);
        $isFeatured = $data['is_featured'] ?? 0;
        $stmt->bindParam(':is_featured', $isFeatured, PDO::PARAM_BOOL);
        $stmt->bindParam(':created_by_user_id', $data['created_by_user_id']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    public function update($id, $data) {
        $sql = "UPDATE tour_packages SET package_type_id = :package_type_id, destination_id = :destination_id, name = :name,
                description = :description, price = :price, duration = :duration, display_image_id = :display_image_id,
                is_featured = :is_featured WHERE tour_package_id = :id";
        $stmt = $this->db->prepare($sql);

        $stmt->bindParam(':package_type_id', $data['package_type_id']);
        $destinationId = !empty($data['destination_id']) ? $data['destination_id'] : null;
        $stmt->bindParam(':destination_id', $destinationId);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':price', $data['price']);
        // Allow empty duration for package types where it's implied
        $duration = !empty($data['duration']) ? $data['duration'] : null;
        $stmt->bindParam(':duration', $duration);
        $stmt->bindParam(':display_image_id', $data['display_image_id']);
        $isFeatured = $data['is_featured'] ?? 0;
        $stmt->bindParam(':is_featured', $isFeatured, PDO::PARAM_BOOL);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getLastInsertedId() {
        return $this->db->lastInsertId();
    }
}

/**
 * Tour Package Destination Model
 */
class TourPackageDestination extends BaseModel {
    protected $table = 'tour_package_destinations';

    protected function getPrimaryKey() {
        return 'tour_package_destination_id';
    }

    public function create($data) {
        $sql = "INSERT INTO tour_package_destinations (tour_package_id, destination_id) VALUES (:tour_package_id, :destination_id)";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $data['tour_package_id']);
        $stmt->bindParam(':destination_id', $data['destination_id']);
        return $stmt->execute();
    }

    public function findByTourPackageId($tourPackageId) {
        $sql = "SELECT tpd.*, d.name as destination_name FROM tour_package_destinations tpd JOIN destinations d ON tpd.destination_id = d.destination_id WHERE tpd.tour_package_id = :tour_package_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $tourPackageId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function deleteByTourPackageId($tourPackageId) {
        $sql = "DELETE FROM tour_package_destinations WHERE tour_package_id = :tour_package_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $tourPackageId);
        return $stmt->execute();
    }
}

/**
 * Contact Info Model
 */
class ContactInfo extends BaseModel {
    protected $table = 'contact_info';
    
    protected function getPrimaryKey() {
        return 'contact_id';
    }
    
    public function getCurrent() {
        $sql = "SELECT * FROM contact_info ORDER BY updated_at DESC LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function update($id, $data) {
        $sql = "UPDATE contact_info SET phone_number = :phone_number, email = :email, booking_email = :booking_email,
                address = :address, working_hours = :working_hours, map_embed_code = :map_embed_code,
                facebook_url = :facebook_url, twitter_url = :twitter_url, instagram_url = :instagram_url,
                tiktok_url = :tiktok_url, youtube_url = :youtube_url,
                last_edited_by_user_id = :last_edited_by_user_id WHERE contact_id = :id";
        $stmt = $this->db->prepare($sql);

        $stmt->bindParam(':phone_number', $data['phone_number']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':booking_email', $data['booking_email']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':working_hours', $data['working_hours']);
        $stmt->bindParam(':map_embed_code', $data['map_embed_code']);
        $stmt->bindParam(':facebook_url', $data['facebook_url']);
        $stmt->bindParam(':twitter_url', $data['twitter_url']);
        $stmt->bindParam(':instagram_url', $data['instagram_url']);
        $stmt->bindParam(':tiktok_url', $data['tiktok_url']);
        $stmt->bindParam(':youtube_url', $data['youtube_url']);
        $stmt->bindParam(':last_edited_by_user_id', $data['last_edited_by_user_id']);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function getContactWithUserInfo() {
        $sql = "SELECT ci.*, u.username as last_edited_by_username
                FROM contact_info ci
                LEFT JOIN users u ON ci.last_edited_by_user_id = u.user_id
                ORDER BY ci.updated_at DESC LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }
}

/**
 * Message Model
 */
class Message extends BaseModel {
    protected $table = 'messages';
    
    protected function getPrimaryKey() {
        return 'message_id';
    }
    
    public function findAllUnread() {
        $sql = "SELECT * FROM messages WHERE is_read = 0 ORDER BY received_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function markAsRead($id) {
        $sql = "UPDATE messages SET is_read = 1 WHERE message_id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }
    
    public function reply($id, $replyContent) {
        $sql = "UPDATE messages SET reply_content = :reply_content, replied_at = NOW() WHERE message_id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':reply_content', $replyContent);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    /**
     * Link message to quote
     */
    public function linkToQuote($messageId, $quoteId) {
        $sql = "UPDATE messages SET quote_id = :quote_id WHERE message_id = :message_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->bindParam(':message_id', $messageId);
        return $stmt->execute();
    }

    /**
     * Find messages by quote ID
     */
    public function findByQuoteId($quoteId) {
        $sql = "SELECT * FROM messages WHERE quote_id = :quote_id ORDER BY received_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Find quote reference in message content or subject
     */
    public function extractQuoteReference($text) {
        // Look for quote reference patterns like QUO2024XXXX
        if (preg_match('/QUO\d{8}/i', $text, $matches)) {
            return strtoupper($matches[0]);
        }
        return null;
    }

    /**
     * Auto-link message to quote based on content
     */
    public function autoLinkToQuote($messageId) {
        $message = $this->findById($messageId);
        if (!$message) {
            return false;
        }

        // Try to find quote reference in subject or content
        $quoteRef = $this->extractQuoteReference($message['subject'] . ' ' . $message['message_content']);

        if ($quoteRef) {
            // Find quote by reference
            $quoteModel = new Quote();
            $quote = $quoteModel->findByReference($quoteRef);

            if ($quote) {
                return $this->linkToQuote($messageId, $quote['quote_id']);
            }
        }

        // Try to match by email address
        $quoteModel = new Quote();
        $quotes = $quoteModel->findByEmail($message['sender_email']);

        if (!empty($quotes)) {
            // Link to the most recent quote
            $latestQuote = $quotes[0];
            return $this->linkToQuote($messageId, $latestQuote['quote_id']);
        }

        return false;
    }
    
    public function create($data) {
        try {
            // Prepare data with defaults
            $senderName = $data['sender_name'];
            $senderEmail = $data['sender_email'];
            $subject = $data['subject'] ?? '';
            $messageContent = $data['message_content'];
            $messageCategory = $data['message_category'] ?? $this->determineMessageCategory($subject);
            $messageType = $data['message_type'] ?? 'incoming';

            // Build SQL with all possible columns that exist in your database
            $sql = "INSERT INTO messages (
                        sender_name, sender_email, subject, message_content,
                        message_category, message_type, conversation_id,
                        email_message_id, in_reply_to, email_thread_id,
                        email_status, original_message_id
                    ) VALUES (
                        :sender_name, :sender_email, :subject, :message_content,
                        :message_category, :message_type, :conversation_id,
                        :email_message_id, :in_reply_to, :email_thread_id,
                        :email_status, :original_message_id
                    )";

            $stmt = $this->db->prepare($sql);

            // Bind all parameters
            $stmt->bindParam(':sender_name', $senderName);
            $stmt->bindParam(':sender_email', $senderEmail);
            $stmt->bindParam(':subject', $subject);
            $stmt->bindParam(':message_content', $messageContent);
            $stmt->bindParam(':message_category', $messageCategory);
            $stmt->bindParam(':message_type', $messageType);

            // Optional fields - use provided values or null
            $conversationId = $data['conversation_id'] ?? null;
            $emailMessageId = $data['email_message_id'] ?? null;
            $inReplyTo = $data['in_reply_to'] ?? null;
            $emailThreadId = $data['email_thread_id'] ?? null;
            $emailStatus = $data['email_status'] ?? null;
            $originalMessageId = $data['original_message_id'] ?? null;

            $stmt->bindParam(':conversation_id', $conversationId);
            $stmt->bindParam(':email_message_id', $emailMessageId);
            $stmt->bindParam(':in_reply_to', $inReplyTo);
            $stmt->bindParam(':email_thread_id', $emailThreadId);
            $stmt->bindParam(':email_status', $emailStatus);
            $stmt->bindParam(':original_message_id', $originalMessageId);

            if ($stmt->execute()) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (Exception $e) {
            error_log("Message create error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Determine message category based on subject or content
     */
    private function determineMessageCategory($subject) {
        $subject = strtolower($subject);

        if (strpos($subject, 'quote') !== false || strpos($subject, 'booking') !== false) {
            return 'quote';
        } elseif (strpos($subject, 'contact') !== false) {
            return 'contact';
        }

        return 'general';
    }



    /**
     * Find messages by email thread
     */
    public function findByEmailThread($emailThreadId) {
        $sql = "SELECT * FROM messages
                WHERE email_thread_id = :email_thread_id
                ORDER BY received_at ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email_thread_id', $emailThreadId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function getStats() {
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN reply_content IS NOT NULL THEN 1 ELSE 0 END) as replied
                FROM messages";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Find messages by category (only works after database migration)
     */
    public function findByCategory($category, $limit = 50, $offset = 0) {
        try {
            // Check if message_category column exists
            $checkColumn = $this->db->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
            $checkColumn->execute();
            if (!$checkColumn->fetch()) {
                throw new Exception("message_category column does not exist. Please run the database migration first.");
            }

            $sql = "SELECT * FROM messages
                    WHERE message_category = :category
                    ORDER BY received_at DESC
                    LIMIT :limit OFFSET :offset";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':category', $category);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("findByCategory error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Count messages by category (only works after database migration)
     */
    public function countByCategory($category) {
        try {
            // Check if message_category column exists
            $checkColumn = $this->db->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
            $checkColumn->execute();
            if (!$checkColumn->fetch()) {
                return 0;
            }

            $sql = "SELECT COUNT(*) as count FROM messages WHERE message_category = :category";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':category', $category);
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'];
        } catch (Exception $e) {
            error_log("countByCategory error: " . $e->getMessage());
            return 0;
        }
    }

    public function getDb() {
        return $this->db;
    }
}

/**
 * Report Model
 */
class Report extends BaseModel {
    protected $table = 'reports';
    
    protected function getPrimaryKey() {
        return 'report_id';
    }
    
    public function generateDashboardStats() {
        $stats = [];

        // Get counts for core content
        $entities = ['destinations', 'tour_packages', 'messages'];
        foreach ($entities as $entity) {
            $sql = "SELECT COUNT(*) as count FROM {$entity}";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats[$entity] = $stmt->fetch()['count'];
        }

        // Get unread messages count
        $sql = "SELECT COUNT(*) as count FROM messages WHERE is_read = 0";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['unread_messages'] = $stmt->fetch()['count'];

        // Get quotes statistics
        $sql = "SELECT
                    COUNT(*) as total_quotes,
                    COUNT(CASE WHEN quote_status = 'pending' THEN 1 END) as pending_quotes,
                    COUNT(CASE WHEN quote_status = 'quoted' THEN 1 END) as quoted_quotes,
                    COUNT(CASE WHEN quote_status = 'accepted' THEN 1 END) as accepted_quotes
                FROM quotes";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $quoteStats = $stmt->fetch();
        $stats = array_merge($stats, $quoteStats);

        // Get bookings statistics
        $sql = "SELECT
                    COUNT(*) as total_bookings,
                    COUNT(CASE WHEN booking_status IN ('confirmed', 'paid', 'completed') THEN 1 END) as active_bookings,
                    COALESCE(SUM(total_amount), 0) as total_booking_value
                FROM bookings";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $bookingStats = $stmt->fetch();
        $stats = array_merge($stats, $bookingStats);

        // Get payments statistics
        $sql = "SELECT
                    COUNT(*) as total_payments,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments,
                    COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END), 0) as total_revenue
                FROM payments";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $paymentStats = $stmt->fetch();
        $stats = array_merge($stats, $paymentStats);

        // Get recent business activity
        $sql = "SELECT 'quote' as type, CONCAT('Quote from ', CONVERT(customer_name USING utf8mb4)) as title, created_at as date FROM quotes
                UNION ALL
                SELECT 'booking' as type, CONCAT('Booking by ', CONVERT(customer_name USING utf8mb4)) as title, created_at as date FROM bookings
                UNION ALL
                SELECT 'payment' as type, CONCAT('Payment $', FORMAT(amount, 2)) as title, created_at as date FROM payments
                UNION ALL
                SELECT 'message' as type, CONCAT(CONVERT(sender_name USING utf8mb4), ' - ', CONVERT(subject USING utf8mb4)) as title, received_at as date FROM messages
                ORDER BY date DESC LIMIT 8";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['recent_activity'] = $stmt->fetchAll();

        return $stats;
    }
    
    public function create($data) {
        $sql = "INSERT INTO reports (report_name, report_data, generated_by_user_id)
                VALUES (:report_name, :report_data, :generated_by_user_id)";
        $stmt = $this->db->prepare($sql);

        $reportData = json_encode($data['report_data']);
        $stmt->bindParam(':report_name', $data['report_name']);
        $stmt->bindParam(':report_data', $reportData);
        $stmt->bindParam(':generated_by_user_id', $data['generated_by_user_id']);

        return $stmt->execute();
    }

    public function getRecentReports($limit = 5) {
        $sql = "SELECT * FROM reports ORDER BY generated_at DESC LIMIT :limit";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function generateCustomReport($reportType, $dateRange) {
        $data = [
            'type' => $reportType,
            'date_range' => $dateRange,
            'generated_at' => date('Y-m-d H:i:s')
        ];

        // Calculate date range
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$dateRange} days"));

        switch ($reportType) {
            case 'financial_summary':
                $data['data'] = $this->getFinancialSummary($startDate, $endDate);
                break;
            case 'booking_summary':
                $data['data'] = $this->getBookingSummary($startDate, $endDate);
                break;
            case 'quote_summary':
                $data['data'] = $this->getQuoteSummary($startDate, $endDate);
                break;
            default:
                $data['data'] = ['error' => 'Unknown report type'];
        }

        return $data;
    }

    public function saveReport($reportName, $reportData, $userId) {
        $data = [
            'report_name' => $reportName,
            'report_data' => $reportData,
            'generated_by_user_id' => $userId
        ];
        return $this->create($data);
    }

    private function getFinancialSummary($startDate, $endDate) {
        $sql = "SELECT
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                    AVG(CASE WHEN payment_status = 'completed' THEN amount ELSE NULL END) as avg_payment,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments
                FROM payments
                WHERE DATE(created_at) BETWEEN :start_date AND :end_date";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function getBookingSummary($startDate, $endDate) {
        $sql = "SELECT
                    COUNT(*) as total_bookings,
                    COUNT(CASE WHEN booking_status IN ('confirmed', 'paid', 'completed') THEN 1 END) as active_bookings,
                    SUM(total_amount) as total_booking_value,
                    AVG(total_amount) as avg_booking_value
                FROM bookings
                WHERE DATE(created_at) BETWEEN :start_date AND :end_date";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function getQuoteSummary($startDate, $endDate) {
        $sql = "SELECT
                    COUNT(*) as total_quotes,
                    COUNT(CASE WHEN quote_status = 'pending' THEN 1 END) as pending_quotes,
                    COUNT(CASE WHEN quote_status = 'accepted' THEN 1 END) as accepted_quotes,
                    ROUND((COUNT(CASE WHEN quote_status = 'accepted' THEN 1 END) * 100.0 / COUNT(*)), 2) as conversion_rate
                FROM quotes
                WHERE DATE(created_at) BETWEEN :start_date AND :end_date";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }


}